'use client';

import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import gradientGL from 'gradient-gl';

const HeroSection = () => {
  const t = useTranslations('hero');

  useEffect(() => {
    // Initialize gradient-gl with the specified seed and target the gradient container
    gradientGL('f2.fc78', '#hero-gradient-bg');
  }, []);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* WebGL Gradient Background */}
      <div id="hero-gradient-bg" className="absolute inset-0 z-0"></div>
      <div className="bg-albatros-indigo-dye absolute left-0 top-0 w-full h-full opacity-70"></div>

      <div className="relative w-full max-w-7xl mx-auto px-6 lg:px-8 z-20">
        <div className="py-32 space-y-16">
          {/* Main Title - Bold and Impactful */}
          <div className="space-y-4">
            <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-[0.9] tracking-tight">
              <span className="block max-w-4xl text-white mb-4">
                {t('title')}
              </span>
            </h1>

            <div className="max-w-3xl">
              <p className="text-xl md:text-2xl text-blue-100/90 leading-relaxed font-light">
                {t('description')}
              </p>
            </div>
          </div>

          {/* Enhanced CTA with modern styling */}
          <div className="flex flex-col sm:flex-row gap-6 pt-8">
            <button className="group relative bg-gradient-to-r from-blue-500 to-cyan-400 text-white px-10 py-4 rounded-full font-semibold text-lg hover:shadow-2xl hover:shadow-blue-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden">
              <span className="relative z-10">Kontaktirajte nas</span>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
